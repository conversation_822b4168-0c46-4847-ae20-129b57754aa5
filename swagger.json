{"openapi": "3.0.0", "info": {"title": "CursorPool API", "description": "CursorPool应用程序的API文档，包括后端API和第三方API", "version": "1.0.0"}, "servers": [{"url": "{baseUrl}", "description": "动态基础URL，根据当前选择的线路确定", "variables": {"baseUrl": {"default": "https://pool.52ai.org/api"}}}], "tags": [{"name": "认证", "description": "用户认证相关接口"}, {"name": "用户", "description": "用户信息和操作相关接口"}, {"name": "账户池", "description": "Cursor账户池相关接口"}, {"name": "系统", "description": "系统相关接口"}, {"name": "Cursor API", "description": "Cursor第三方API"}], "paths": {"/checkUser": {"post": {"tags": ["认证"], "summary": "检查用户是否存在", "description": "根据邮箱检查用户是否存在", "operationId": "checkUser", "requestBody": {"required": true, "content": {"application/x-www-form-urlencoded": {"schema": {"type": "object", "properties": {"email": {"type": "string", "description": "用户邮箱"}}, "required": ["email"]}}}}, "responses": {"200": {"description": "成功响应", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/register/sendEmailCode": {"post": {"tags": ["认证"], "summary": "发送验证码", "description": "发送邮箱验证码，用于注册或重置密码", "operationId": "sendCode", "requestBody": {"required": true, "content": {"application/x-www-form-urlencoded": {"schema": {"type": "object", "properties": {"email": {"type": "string", "description": "用户邮箱"}, "type": {"type": "string", "description": "验证码类型，register或reset"}}, "required": ["email", "type"]}}}}, "responses": {"200": {"description": "成功响应", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/emailRegister": {"post": {"tags": ["认证"], "summary": "邮箱注册", "description": "通过邮箱和验证码注册新用户", "operationId": "register", "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"email": {"type": "string", "description": "用户邮箱"}, "code": {"type": "string", "description": "验证码"}, "password": {"type": "string", "description": "密码"}, "spread": {"type": "string", "description": "推广码", "default": "0"}}, "required": ["email", "code", "password"]}}}}, "responses": {"200": {"description": "成功响应", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterResponse"}}}}}}}, "/login": {"post": {"tags": ["认证"], "summary": "用户登录", "description": "使用账号密码登录", "operationId": "login", "requestBody": {"required": true, "content": {"application/x-www-form-urlencoded": {"schema": {"type": "object", "properties": {"account": {"type": "string", "description": "用户账号/邮箱"}, "password": {"type": "string", "description": "密码"}, "spread": {"type": "string", "description": "推广码", "default": "0"}}, "required": ["account", "password"]}}}}, "responses": {"200": {"description": "成功响应", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginResponse"}}}}}}}, "/user": {"get": {"tags": ["用户"], "summary": "获取用户信息", "description": "获取当前登录用户的信息", "operationId": "getUserInfo", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "成功响应", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserInfoResponse"}}}}}}}, "/user/activate": {"post": {"tags": ["用户"], "summary": "激活账户", "description": "使用激活码激活账户", "operationId": "activate", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/x-www-form-urlencoded": {"schema": {"type": "object", "properties": {"code": {"type": "string", "description": "激活码"}}, "required": ["code"]}}}}, "responses": {"200": {"description": "成功响应", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/user/updatePassword": {"post": {"tags": ["用户"], "summary": "修改密码", "description": "修改当前用户的密码", "operationId": "changePassword", "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/x-www-form-urlencoded": {"schema": {"type": "object", "properties": {"old_password": {"type": "string", "description": "旧密码"}, "new_password": {"type": "string", "description": "新密码"}, "confirm_password": {"type": "string", "description": "确认新密码"}}, "required": ["old_password", "new_password", "confirm_password"]}}}}, "responses": {"200": {"description": "成功响应", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/accountpool/get": {"get": {"tags": ["账户池"], "summary": "获取账户信息", "description": "获取Cursor账户池中的账户信息", "operationId": "getAccount", "security": [{"bearerAuth": []}], "parameters": [{"name": "account", "in": "query", "description": "指定账户", "required": false, "schema": {"type": "string"}}, {"name": "usage_count", "in": "query", "description": "使用次数", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "成功响应", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccountPoolResponse"}}}}}}}, "/emailResetPassword": {"post": {"tags": ["认证"], "summary": "重置密码", "description": "通过邮箱验证码重置密码", "operationId": "resetPassword", "requestBody": {"required": true, "content": {"application/x-www-form-urlencoded": {"schema": {"type": "object", "properties": {"email": {"type": "string", "description": "用户邮箱"}, "code": {"type": "string", "description": "验证码"}, "password": {"type": "string", "description": "新密码"}}, "required": ["email", "code", "password"]}}}}, "responses": {"200": {"description": "成功响应", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/public/info": {"get": {"tags": ["系统"], "summary": "获取公告信息", "description": "获取系统公告信息", "operationId": "getPublicInfo", "responses": {"200": {"description": "成功响应", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PublicInfoResponse"}}}}}}}, "/report": {"post": {"tags": ["系统"], "summary": "报告错误", "description": "提交错误报告", "operationId": "reportBug", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BugReportRequest"}}}}, "responses": {"200": {"description": "成功响应", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/article/list/{page}": {"get": {"tags": ["系统"], "summary": "获取公告列表", "description": "获取系统公告列表", "operationId": "getArticleList", "parameters": [{"name": "page", "in": "path", "description": "页码", "required": true, "schema": {"type": "integer", "default": 1}}], "responses": {"200": {"description": "成功响应", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ArticleListResponse"}}}}}}}, "/version": {"get": {"tags": ["系统"], "summary": "获取版本信息", "description": "获取API服务版本信息，用于测试线路延迟", "operationId": "getVersion", "responses": {"200": {"description": "成功响应", "content": {"application/json": {"schema": {"type": "object", "properties": {"version": {"type": "string"}}}}}}}}}, "https://www.cursor.com/api/usage": {"get": {"tags": ["Cursor API"], "summary": "获取Cursor使用情况", "description": "获取Cursor账号的使用情况", "operationId": "getUsage", "security": [{"cookieAuth": []}], "responses": {"200": {"description": "成功响应", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CursorUsageInfo"}}}}}}}}, "components": {"schemas": {"ApiResponse": {"type": "object", "properties": {"status": {"type": "integer", "description": "状态码，200表示成功"}, "msg": {"type": "string", "description": "响应消息"}, "data": {"type": "object", "description": "响应数据"}, "code": {"type": "string", "description": "业务代码"}}}, "RegisterResponse": {"type": "object", "properties": {"status": {"type": "integer"}, "msg": {"type": "string"}, "data": {"type": "object", "properties": {"token": {"type": "string", "description": "认证令牌"}, "expires_time": {"type": "integer", "description": "过期时间戳"}}}, "code": {"type": "string"}}}, "LoginResponse": {"type": "object", "properties": {"status": {"type": "integer"}, "msg": {"type": "string"}, "data": {"type": "object", "properties": {"token": {"type": "string", "description": "认证令牌"}, "user_info": {"$ref": "#/components/schemas/UserInfo"}}}, "code": {"type": "string"}}}, "UserInfo": {"type": "object", "properties": {"totalCount": {"type": "integer", "description": "用户总额度"}, "usedCount": {"type": "integer", "description": "已使用额度"}, "expireTime": {"type": "string", "description": "过期时间"}, "level": {"type": "integer", "description": "用户等级"}, "isExpired": {"type": "boolean", "description": "是否已过期"}, "username": {"type": "string", "description": "用户名"}, "code_level": {"type": "string", "description": "用户级别文本"}, "code_status": {"type": "integer", "description": "激活码状态"}}}, "UserInfoResponse": {"type": "object", "properties": {"status": {"type": "integer"}, "msg": {"type": "string"}, "data": {"type": "object", "properties": {"models": {"type": "array", "items": {"$ref": "#/components/schemas/GptModelUsage"}}}}, "code": {"type": "string"}}}, "GptModelUsage": {"type": "object", "properties": {"numRequests": {"type": "integer", "description": "请求次数"}, "numRequestsTotal": {"type": "integer", "description": "总请求次数"}, "numTokens": {"type": "integer", "description": "Token数量"}, "maxRequestUsage": {"type": "integer", "description": "最大请求使用量"}, "maxTokenUsage": {"type": "integer", "description": "最大Token使用量"}}}, "AccountPoolResponse": {"type": "object", "properties": {"status": {"type": "integer"}, "msg": {"type": "string"}, "data": {"type": "object", "properties": {"success": {"type": "boolean"}, "account_info": {"$ref": "#/components/schemas/AccountInfo"}, "activation_code": {"$ref": "#/components/schemas/ActivationCode"}}}, "code": {"type": "string"}}}, "AccountInfo": {"type": "object", "properties": {"id": {"type": "integer", "description": "账户ID"}, "account": {"type": "string", "description": "账户名"}, "password": {"type": "string", "description": "密码"}, "token": {"type": "string", "description": "令牌"}, "usage_count": {"type": "integer", "description": "使用次数"}, "status": {"type": "integer", "description": "状态"}, "create_time": {"type": "string", "description": "创建时间"}, "distributed_time": {"type": "string", "description": "分配时间"}, "update_time": {"type": "string", "description": "更新时间"}}}, "ActivationCode": {"type": "object", "properties": {"id": {"type": "integer"}, "code": {"type": "string"}, "type": {"type": "integer"}, "name": {"type": "string"}, "level": {"type": "integer"}, "duration": {"type": "integer"}, "max_uses": {"type": "integer"}, "used_count": {"type": "integer"}, "status": {"type": "integer"}, "notes": {"type": "string"}, "activated_at": {"type": "string"}, "expired_at": {"type": "string"}}}, "PublicInfoResponse": {"type": "object", "properties": {"status": {"type": "integer"}, "msg": {"type": "string"}, "data": {"$ref": "#/components/schemas/PublicInfo"}, "code": {"type": "string"}}}, "PublicInfo": {"type": "object", "properties": {"type": {"type": "string"}, "closeable": {"type": "boolean"}, "props": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}}}, "actions": {"type": "array", "items": {"type": "object", "properties": {"type": {"type": "string"}, "text": {"type": "string"}, "url": {"type": "string"}}}}}}, "BugReportRequest": {"type": "object", "properties": {"api_key": {"type": "string", "description": "API密钥"}, "app_version": {"type": "string", "description": "应用版本"}, "os_version": {"type": "string", "description": "操作系统版本"}, "device_model": {"type": "string", "description": "设备型号"}, "cursor_version": {"type": "string", "description": "Cursor<PERSON>"}, "bug_description": {"type": "string", "description": "错误描述"}, "occurrence_time": {"type": "string", "description": "发生时间"}, "screenshot_urls": {"type": "array", "items": {"type": "string"}, "description": "截图URL列表"}, "severity": {"type": "string", "description": "严重程度"}}, "required": ["app_version", "os_version", "device_model", "cursor_version", "bug_description", "occurrence_time", "severity"]}, "ArticleListResponse": {"type": "object", "properties": {"status": {"type": "integer"}, "msg": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Article"}}, "code": {"type": "string"}}}, "Article": {"type": "object", "properties": {"id": {"type": "integer", "description": "文章ID"}, "title": {"type": "string", "description": "文章标题"}, "content": {"type": "string", "description": "文章内容"}}}, "CursorUsageInfo": {"type": "object", "properties": {"gpt-4": {"$ref": "#/components/schemas/CursorModelUsage"}, "gpt-3.5-turbo": {"$ref": "#/components/schemas/CursorModelUsage"}, "gpt-4-32k": {"$ref": "#/components/schemas/CursorModelUsage"}, "startOfMonth": {"type": "string", "description": "月初时间"}}}, "CursorModelUsage": {"type": "object", "properties": {"numRequests": {"type": "integer", "description": "请求次数"}, "numRequestsTotal": {"type": "integer", "description": "总请求次数"}, "numTokens": {"type": "integer", "description": "Token数量"}, "maxRequestUsage": {"type": "integer", "description": "最大请求使用量"}, "maxTokenUsage": {"type": "integer", "description": "最大Token使用量"}}}}, "securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "使用JWT令牌进行身份验证"}, "cookieAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "<PERSON><PERSON>", "description": "使用Cookie进行身份验证，格式为WorkosCursorSessionToken={userId}%3A%3A{token}"}}}}