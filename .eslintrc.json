{
  "root": true,
  "extends": [
    "plugin:vue/vue3-recommended",
    "eslint:recommended",
    "@vue/eslint-config-typescript",
    "@vue/eslint-config-prettier"
  ],
  "parserOptions": {
    "ecmaVersion": "latest"
  },
  "rules": {
    "vue/multi-word-component-names": "off",
    "vue/no-v-html": "off",
    "vue/max-attributes-per-line": ["error", {
      "singleline": 10,
      "multiline": 1
    }],
    "vue/first-attribute-linebreak": "off",
    // todo 临时屏蔽
    "@typescript-eslint/no-explicit-any": "off",
    "@typescript-eslint/no-unused-vars": ["warn", {
      "argsIgnorePattern": "^_",
      "varsIgnorePattern": "^_"
    }]
  }
} 