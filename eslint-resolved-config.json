{"env": {"browser": true, "es6": true}, "globals": {}, "parser": "D:\\full-stack\\cursorpool\\node_modules\\.pnpm\\vue-eslint-parser@9.4.3_eslint@8.57.1\\node_modules\\vue-eslint-parser\\index.js", "parserOptions": {"ecmaVersion": "latest", "parser": {"js": "espree", "jsx": "espree", "cjs": "espree", "mjs": "espree", "ts": "D:\\full-stack\\cursorpool\\node_modules\\.pnpm\\@typescript-eslint+parser@6_78441eab347b5bbf23195f733ce3a592\\node_modules\\@typescript-eslint\\parser\\dist\\index.js", "tsx": "D:\\full-stack\\cursorpool\\node_modules\\.pnpm\\@typescript-eslint+parser@6_78441eab347b5bbf23195f733ce3a592\\node_modules\\@typescript-eslint\\parser\\dist\\index.js", "cts": "D:\\full-stack\\cursorpool\\node_modules\\.pnpm\\@typescript-eslint+parser@6_78441eab347b5bbf23195f733ce3a592\\node_modules\\@typescript-eslint\\parser\\dist\\index.js", "mts": "D:\\full-stack\\cursorpool\\node_modules\\.pnpm\\@typescript-eslint+parser@6_78441eab347b5bbf23195f733ce3a592\\node_modules\\@typescript-eslint\\parser\\dist\\index.js"}, "extraFileExtensions": [".vue"], "ecmaFeatures": {"jsx": true}, "sourceType": "module"}, "plugins": ["vue", "@typescript-eslint", "prettier"], "rules": {"vue/multi-word-component-names": ["off"], "vue/no-v-html": ["off"], "vue/max-attributes-per-line": ["error", {"singleline": 10, "multiline": 1}], "@typescript-eslint/no-explicit-any": ["off"], "@typescript-eslint/no-unused-vars": ["warn", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_"}], "prettier/prettier": ["warn"], "arrow-body-style": ["off"], "prefer-arrow-callback": ["off"], "curly": [0], "lines-around-comment": [0], "max-len": [0], "no-confusing-arrow": [0], "no-mixed-operators": [0], "no-tabs": [0], "no-unexpected-multiline": [0], "quotes": [0], "@typescript-eslint/lines-around-comment": [0], "@typescript-eslint/quotes": [0], "babel/quotes": [0], "vue/html-self-closing": [0], "vue/max-len": [0], "array-bracket-newline": ["off"], "array-bracket-spacing": ["off"], "array-element-newline": ["off"], "arrow-parens": ["off"], "arrow-spacing": ["off"], "block-spacing": ["off"], "brace-style": ["off"], "comma-dangle": ["off"], "comma-spacing": ["off"], "comma-style": ["off"], "computed-property-spacing": ["off"], "dot-location": ["off"], "eol-last": ["off"], "func-call-spacing": ["off"], "function-call-argument-newline": ["off"], "function-paren-newline": ["off"], "generator-star-spacing": ["off"], "implicit-arrow-linebreak": ["off"], "indent": ["off"], "jsx-quotes": ["off"], "key-spacing": ["off"], "keyword-spacing": ["off"], "linebreak-style": ["off"], "max-statements-per-line": ["off"], "multiline-ternary": ["off"], "newline-per-chained-call": ["off"], "new-parens": ["off"], "no-extra-parens": ["off"], "no-extra-semi": ["off"], "no-floating-decimal": ["off"], "no-mixed-spaces-and-tabs": ["off"], "no-multi-spaces": ["off"], "no-multiple-empty-lines": ["off"], "no-trailing-spaces": ["off"], "no-whitespace-before-property": ["off"], "nonblock-statement-body-position": ["off"], "object-curly-newline": ["off"], "object-curly-spacing": ["off"], "object-property-newline": ["off"], "one-var-declaration-per-line": ["off"], "operator-linebreak": ["off"], "padded-blocks": ["off"], "quote-props": ["off"], "rest-spread-spacing": ["off"], "semi": ["off"], "semi-spacing": ["off"], "semi-style": ["off"], "space-before-blocks": ["off"], "space-before-function-paren": ["off"], "space-in-parens": ["off"], "space-infix-ops": ["off"], "space-unary-ops": ["off"], "switch-colon-spacing": ["off"], "template-curly-spacing": ["off"], "template-tag-spacing": ["off"], "unicode-bom": ["off"], "wrap-iife": ["off"], "wrap-regex": ["off"], "yield-star-spacing": ["off"], "@babel/object-curly-spacing": ["off"], "@babel/semi": ["off"], "@typescript-eslint/block-spacing": ["off"], "@typescript-eslint/brace-style": ["off"], "@typescript-eslint/comma-dangle": ["off"], "@typescript-eslint/comma-spacing": ["off"], "@typescript-eslint/func-call-spacing": ["off"], "@typescript-eslint/indent": ["off"], "@typescript-eslint/key-spacing": ["off"], "@typescript-eslint/keyword-spacing": ["off"], "@typescript-eslint/member-delimiter-style": ["off"], "@typescript-eslint/no-extra-parens": ["off"], "@typescript-eslint/no-extra-semi": ["off"], "@typescript-eslint/object-curly-spacing": ["off"], "@typescript-eslint/semi": ["off"], "@typescript-eslint/space-before-blocks": ["off"], "@typescript-eslint/space-before-function-paren": ["off"], "@typescript-eslint/space-infix-ops": ["off"], "@typescript-eslint/type-annotation-spacing": ["off"], "babel/object-curly-spacing": ["off"], "babel/semi": ["off"], "flowtype/boolean-style": ["off"], "flowtype/delimiter-dangle": ["off"], "flowtype/generic-spacing": ["off"], "flowtype/object-type-curly-spacing": ["off"], "flowtype/object-type-delimiter": ["off"], "flowtype/quotes": ["off"], "flowtype/semi": ["off"], "flowtype/space-after-type-colon": ["off"], "flowtype/space-before-generic-bracket": ["off"], "flowtype/space-before-type-colon": ["off"], "flowtype/union-intersection-spacing": ["off"], "react/jsx-child-element-spacing": ["off"], "react/jsx-closing-bracket-location": ["off"], "react/jsx-closing-tag-location": ["off"], "react/jsx-curly-newline": ["off"], "react/jsx-curly-spacing": ["off"], "react/jsx-equals-spacing": ["off"], "react/jsx-first-prop-new-line": ["off"], "react/jsx-indent": ["off"], "react/jsx-indent-props": ["off"], "react/jsx-max-props-per-line": ["off"], "react/jsx-newline": ["off"], "react/jsx-one-expression-per-line": ["off"], "react/jsx-props-no-multi-spaces": ["off"], "react/jsx-tag-spacing": ["off"], "react/jsx-wrap-multilines": ["off"], "standard/array-bracket-even-spacing": ["off"], "standard/computed-property-even-spacing": ["off"], "standard/object-curly-even-spacing": ["off"], "unicorn/empty-brace-spaces": ["off"], "unicorn/no-nested-ternary": ["off"], "unicorn/number-literal-case": ["off"], "vue/array-bracket-newline": ["off"], "vue/array-bracket-spacing": ["off"], "vue/array-element-newline": ["off"], "vue/arrow-spacing": ["off"], "vue/block-spacing": ["off"], "vue/block-tag-newline": ["off"], "vue/brace-style": ["off"], "vue/comma-dangle": ["off"], "vue/comma-spacing": ["off"], "vue/comma-style": ["off"], "vue/dot-location": ["off"], "vue/func-call-spacing": ["off"], "vue/html-closing-bracket-newline": ["off"], "vue/html-closing-bracket-spacing": ["off"], "vue/html-end-tags": ["off"], "vue/html-indent": ["off"], "vue/html-quotes": ["off"], "vue/key-spacing": ["off"], "vue/keyword-spacing": ["off"], "vue/multiline-html-element-content-newline": ["off"], "vue/multiline-ternary": ["off"], "vue/mustache-interpolation-spacing": ["off"], "vue/no-extra-parens": ["off"], "vue/no-multi-spaces": ["off"], "vue/no-spaces-around-equal-signs-in-attribute": ["off"], "vue/object-curly-newline": ["off"], "vue/object-curly-spacing": ["off"], "vue/object-property-newline": ["off"], "vue/operator-linebreak": ["off"], "vue/quote-props": ["off"], "vue/script-indent": ["off"], "vue/singleline-html-element-content-newline": ["off"], "vue/space-in-parens": ["off"], "vue/space-infix-ops": ["off"], "vue/space-unary-ops": ["off"], "vue/template-curly-spacing": ["off"], "generator-star": ["off"], "indent-legacy": ["off"], "no-arrow-condition": ["off"], "no-comma-dangle": ["off"], "no-reserved-keys": ["off"], "no-space-before-semi": ["off"], "no-spaced-func": ["off"], "no-wrap-func": ["off"], "space-after-function-name": ["off"], "space-after-keywords": ["off"], "space-before-function-parentheses": ["off"], "space-before-keywords": ["off"], "space-in-brackets": ["off"], "space-return-throw-case": ["off"], "space-unary-word-ops": ["off"], "react/jsx-space-before-closing": ["off"], "no-unused-vars": ["off"], "no-undef": ["off"], "constructor-super": ["error"], "for-direction": ["error"], "getter-return": ["error"], "no-async-promise-executor": ["error"], "no-case-declarations": ["error"], "no-class-assign": ["error"], "no-compare-neg-zero": ["error"], "no-cond-assign": ["error"], "no-const-assign": ["error"], "no-constant-condition": ["error"], "no-control-regex": ["error"], "no-debugger": ["error"], "no-delete-var": ["error"], "no-dupe-args": ["error"], "no-dupe-class-members": ["error"], "no-dupe-else-if": ["error"], "no-dupe-keys": ["error"], "no-duplicate-case": ["error"], "no-empty": ["error"], "no-empty-character-class": ["error"], "no-empty-pattern": ["error"], "no-ex-assign": ["error"], "no-extra-boolean-cast": ["error"], "no-fallthrough": ["error"], "no-func-assign": ["error"], "no-global-assign": ["error"], "no-import-assign": ["error"], "no-inner-declarations": ["error"], "no-invalid-regexp": ["error"], "no-irregular-whitespace": ["error"], "no-loss-of-precision": ["error"], "no-misleading-character-class": ["error"], "no-new-symbol": ["error"], "no-nonoctal-decimal-escape": ["error"], "no-obj-calls": ["error"], "no-octal": ["error"], "no-prototype-builtins": ["error"], "no-redeclare": ["error"], "no-regex-spaces": ["error"], "no-self-assign": ["error"], "no-setter-return": ["error"], "no-shadow-restricted-names": ["error"], "no-sparse-arrays": ["error"], "no-this-before-super": ["error"], "no-unreachable": ["error"], "no-unsafe-finally": ["error"], "no-unsafe-negation": ["error"], "no-unsafe-optional-chaining": ["error"], "no-unused-labels": ["error"], "no-useless-backreference": ["error"], "no-useless-catch": ["error"], "no-useless-escape": ["error"], "no-with": ["error"], "require-yield": ["error"], "use-isnan": ["error"], "valid-typeof": ["error"], "vue/attributes-order": ["warn"], "vue/component-tags-order": ["warn"], "vue/no-lone-template": ["warn"], "vue/no-multiple-slot-args": ["warn"], "vue/order-in-components": ["warn"], "vue/this-in-template": ["warn"], "vue/attribute-hyphenation": ["warn"], "vue/component-definition-name-casing": ["warn"], "vue/first-attribute-linebreak": ["warn"], "vue/no-template-shadow": ["warn"], "vue/one-component-per-file": ["warn"], "vue/prop-name-casing": ["warn"], "vue/require-default-prop": ["warn"], "vue/require-explicit-emits": ["warn"], "vue/require-prop-types": ["warn"], "vue/v-bind-style": ["warn"], "vue/v-on-event-hyphenation": ["warn", "always", {"autofix": true}], "vue/v-on-style": ["warn"], "vue/v-slot-style": ["warn"], "vue/no-arrow-functions-in-watch": ["error"], "vue/no-async-in-computed-properties": ["error"], "vue/no-child-content": ["error"], "vue/no-computed-properties-in-data": ["error"], "vue/no-deprecated-data-object-declaration": ["error"], "vue/no-deprecated-destroyed-lifecycle": ["error"], "vue/no-deprecated-dollar-listeners-api": ["error"], "vue/no-deprecated-dollar-scopedslots-api": ["error"], "vue/no-deprecated-events-api": ["error"], "vue/no-deprecated-filter": ["error"], "vue/no-deprecated-functional-template": ["error"], "vue/no-deprecated-html-element-is": ["error"], "vue/no-deprecated-inline-template": ["error"], "vue/no-deprecated-props-default-this": ["error"], "vue/no-deprecated-router-link-tag-prop": ["error"], "vue/no-deprecated-scope-attribute": ["error"], "vue/no-deprecated-slot-attribute": ["error"], "vue/no-deprecated-slot-scope-attribute": ["error"], "vue/no-deprecated-v-bind-sync": ["error"], "vue/no-deprecated-v-is": ["error"], "vue/no-deprecated-v-on-native-modifier": ["error"], "vue/no-deprecated-v-on-number-modifiers": ["error"], "vue/no-deprecated-vue-config-keycodes": ["error"], "vue/no-dupe-keys": ["error"], "vue/no-dupe-v-else-if": ["error"], "vue/no-duplicate-attributes": ["error"], "vue/no-export-in-script-setup": ["error"], "vue/no-expose-after-await": ["error"], "vue/no-lifecycle-after-await": ["error"], "vue/no-mutating-props": ["error"], "vue/no-parsing-error": ["error"], "vue/no-ref-as-operand": ["error"], "vue/no-reserved-component-names": ["error"], "vue/no-reserved-keys": ["error"], "vue/no-reserved-props": ["error"], "vue/no-shared-component-data": ["error"], "vue/no-side-effects-in-computed-properties": ["error"], "vue/no-template-key": ["error"], "vue/no-textarea-mustache": ["error"], "vue/no-unused-components": ["error"], "vue/no-unused-vars": ["error"], "vue/no-use-computed-property-like-method": ["error"], "vue/no-use-v-if-with-v-for": ["error"], "vue/no-useless-template-attributes": ["error"], "vue/no-v-for-template-key-on-child": ["error"], "vue/no-v-text-v-html-on-component": ["error"], "vue/no-watch-after-await": ["error"], "vue/prefer-import-from-vue": ["error"], "vue/require-component-is": ["error"], "vue/require-prop-type-constructor": ["error"], "vue/require-render-return": ["error"], "vue/require-slots-as-functions": ["error"], "vue/require-toggle-inside-transition": ["error"], "vue/require-v-for-key": ["error"], "vue/require-valid-default-prop": ["error"], "vue/return-in-computed-property": ["error"], "vue/return-in-emits-validator": ["error"], "vue/use-v-on-exact": ["error"], "vue/valid-attribute-name": ["error"], "vue/valid-define-emits": ["error"], "vue/valid-define-props": ["error"], "vue/valid-next-tick": ["error"], "vue/valid-template-root": ["error"], "vue/valid-v-bind": ["error"], "vue/valid-v-cloak": ["error"], "vue/valid-v-else-if": ["error"], "vue/valid-v-else": ["error"], "vue/valid-v-for": ["error"], "vue/valid-v-html": ["error"], "vue/valid-v-if": ["error"], "vue/valid-v-is": ["error"], "vue/valid-v-memo": ["error"], "vue/valid-v-model": ["error"], "vue/valid-v-on": ["error"], "vue/valid-v-once": ["error"], "vue/valid-v-pre": ["error"], "vue/valid-v-show": ["error"], "vue/valid-v-slot": ["error"], "vue/valid-v-text": ["error"], "vue/comment-directive": ["error"], "vue/jsx-uses-vars": ["error"]}, "settings": {}, "ignorePatterns": []}