{"$schema": "https://schema.tauri.app/config/2", "productName": "cursor-pool", "version": "../package.json", "identifier": "com.cursor-pool.app", "build": {"beforeDevCommand": "pnpm run dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "pnpm run build", "frontendDist": "../dist"}, "app": {"windows": [{"title": "cursor-pool", "minWidth": 860, "minHeight": 600, "decorations": false, "minimizable": true, "closable": true, "visible": true, "label": "main", "center": true, "transparent": true, "titleBarStyle": "Overlay"}], "security": {"csp": null, "capabilities": ["main-capability"]}, "macOSPrivateApi": true}, "bundle": {"active": true, "category": "DeveloperTool", "copyright": "", "targets": "all", "createUpdaterArtifacts": true, "icon": ["icons/32x32.png", "icons/40x40.png", "icons/48x48.png", "icons/80x80.png", "icons/120x120.png", "icons/128x128.png", "icons/icon.icns", "icons/icon.ico"], "windows": {"certificateThumbprint": null, "digestAlgorithm": "sha256", "timestampUrl": "", "wix": {"language": ["zh-CN", "en-US"]}, "webviewInstallMode": {"type": "downloadBootstrapper"}, "nsis": {"languages": ["SimpChinese", "English"], "displayLanguageSelector": true}}, "macOS": {"entitlements": null, "exceptionDomain": "", "frameworks": [], "providerShortName": null, "signingIdentity": null, "minimumSystemVersion": "10.13"}, "linux": {"deb": {"depends": []}, "rpm": {"depends": []}, "appimage": {"bundleMediaFramework": true}}}, "plugins": {"os": {"open": true}, "shell": {"open": true}, "updater": {"active": true, "pubkey": "dW50cnVzdGVkIGNvbW1lbnQ6IG1pbmlzaWduIHB1YmxpYyBrZXk6IEM5NjUxODYwQkUzMkY2RTYKUldUbTlqSytZQmhseVIvWXg0c1RLakxCajBpdGQzeGxUbVMyaGYyRWZXdXdvNURtTW5lZXBVVWQK", "endpoints": ["https://vip.123pan.cn/**********/updater/latest.json", "https://github.com/Cloxl/CursorPool_Client/releases/latest/download/latest.json", "https://gh-proxy.com/github.com/Cloxl/CursorPool_Client/releases/latest/download/latest.json"], "windows": {"installMode": "passive"}, "dangerousInsecureTransportProtocol": true}}}